package ai

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"hash/fnv"
	"slices"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"github.com/samber/lo/mutable"
	"github.com/sashabaranov/go-openai"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	"k8s.io/client-go/rest"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai/clients"
	"github.com/akuityio/akuity-platform/internal/services/ai/functions"
	"github.com/akuityio/akuity-platform/internal/services/ai/reposet"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutils "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/metadata"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
	metadatautil "github.com/akuityio/akuity-platform/pkg/utils/grpc/metadata"

	_ "embed"
)

//go:embed prompt.md
var systemPrompt string

type Service struct {
	db              *sql.DB
	aiClient        *clients.AIClientSet
	functionCtrl    *functions.Controller
	resourceRepoSet reposet.ResourceRepoSet
	organizationID  string
	instanceID      string
	kargoInstanceID string
	cfg             config.AIConfig
	log             logr.Logger
	k8sClientSet    *clients.K8sClientSet
	hostRestConfig  *rest.Config
	resSvc          *k8sresource.Service
	featureStatuses *featuresv1.FeatureStatuses
}

type ServiceOption func(*Service)

func NewService(db *sql.DB, rs client.RepoSet, featureStatuses *featuresv1.FeatureStatuses, organizationID string, cfg config.AIConfig, hostRestConfig *rest.Config, log logr.Logger, opts ...ServiceOption) (*Service, error) {
	resourceRepoSet := reposet.NewResourceRepoSet(rs, organizationID)
	resSvc := k8sresource.NewServiceWithOptions(rs, db, organizationID, k8sresource.WithLogger(log))

	aiClient := clients.NewAIClientSet(rs, db)
	if cfg.AnthropicAPIKey != "" {
		aiClient.AddClient(ai.ProviderAnthropic, ai.NewAnthropicClient(cfg.AnthropicAPIKey))
	}
	if cfg.OpenAIAPIKey != "" {
		aiClient.AddClient(ai.ProviderOpenAI, ai.NewOpenAIClient(cfg.OpenAIAPIKey))
	}
	k8sClientSet, err := clients.NewK8sClientSet(resourceRepoSet, hostRestConfig, log)
	if err != nil {
		return nil, err
	}

	srv := &Service{
		db:              db,
		aiClient:        aiClient,
		resSvc:          resSvc,
		featureStatuses: featureStatuses,
		resourceRepoSet: resourceRepoSet,
		organizationID:  organizationID,
		cfg:             cfg,
		log:             log,
		k8sClientSet:    k8sClientSet,
		hostRestConfig:  hostRestConfig,
	}
	for _, opt := range opts {
		opt(srv)
	}
	return srv, nil
}

func (s *Service) getFunctionController(ctx context.Context) (*functions.Controller, error) {
	if s.functionCtrl == nil {
		ctrl, err := functions.NewController(
			ctx, s.organizationID, s.resourceRepoSet, s.resSvc, s.log, s.aiClient, s.k8sClientSet, s.featureStatuses)
		if err != nil {
			return nil, err
		}
		s.functionCtrl = ctrl
	}
	return s.functionCtrl, nil
}

func ServiceOptionWithInstanceID(instanceID string) ServiceOption {
	return func(s *Service) {
		s.instanceID = instanceID
	}
}

func ServiceOptionWithKargoInstanceID(instanceID string) ServiceOption {
	return func(s *Service) {
		s.kargoInstanceID = instanceID
	}
}

func (s *Service) CreateConversation(ctx context.Context, actor *accesscontrol.Actor, instanceID string, contexts []*organizationv1.AIMessageContext, runbooks []string) (*organizationv1.AIConversation, error) {
	conversation := &models.AiConversation{
		OrganizationID: s.organizationID,
		UserID:         s.generateUserID(actor),
	}
	if err := conversation.SetContexts(lo.Map(contexts, func(item *organizationv1.AIMessageContext, index int) models.ConversationContext {
		return mapAPIToContext(item)
	})); err != nil {
		return nil, err
	}
	if err := conversation.SetRunbooks(runbooks); err != nil {
		return nil, err
	}
	if instanceID != "" {
		platform := metadata.ExtractPlatform(ctx)
		if platform == metadatautil.PlatformKargo {
			conversation.KargoInstanceID = null.StringFrom(instanceID)
		} else {
			conversation.InstanceID = null.StringFrom(instanceID)
		}
	}

	now := time.Now()
	convMeta := &models.AIConversationMetadata{
		LastProcessTime: now,
	}
	if err := conversation.SetMetadata(convMeta); err != nil {
		return nil, err
	}

	var contextsMsg string
	if len(contexts) == 0 {
		contextsMsg = "No contexts specified"
	} else {
		contextsJSON, err := json.Marshal(contexts)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to marshal contexts: %v", err)
		}
		contextsMsg = fmt.Sprintf("Contexts: %s", string(contextsJSON))
	}

	/*var runbooksMsg string
	if len(runbooks) == 0 {
		runbooksMsg = "No runbooks specified"
	} else {
		runbooksJSON, err := json.Marshal(runbooks)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to marshal runbooks: %v", err)
		}
		runbooksMsg = fmt.Sprintf("Runbooks: %s", string(runbooksJSON))
	}*/

	var messages []*models.AIConversationMessage
	for _, in := range []ai.Message{{
		Role:    ai.MessageRoleAssistant,
		Content: s.getInitialAssistantMessage(),
	}, {
		Role:    ai.MessageRoleDeveloper,
		Content: contextsMsg,
	}} {
		msg, err := newConversationMessage(in)
		if err != nil {
			return nil, err
		}
		msg.Processed = true
		messages = append(messages, msg)
	}

	if err := conversation.SetMessages(messages); err != nil {
		return nil, err
	}

	conversation.LastUpdateTimestamp = now
	if err := s.resourceRepoSet.AIConversations().Create(ctx, conversation); err != nil {
		return nil, err
	}
	var instance *models.ArgoCDInstance
	var err error
	if !conversation.InstanceID.IsZero() {
		if instance, err = s.resourceRepoSet.ArgoCDInstances(
			models.ArgoCDInstanceWhere.ID.EQ(conversation.InstanceID.String),
		).One(ctx); err != nil {
			return nil, err
		}
	}
	apiConversation, err := s.mapConversationToAPI(ctx, conversation, actor, instance, true)
	if err != nil {
		return nil, err
	}
	return apiConversation, nil
}

func (s *Service) inferInstanceFromConversation(conversation *models.AiConversation) (string, error) {
	ctxs, err := conversation.GetContexts()
	if err != nil {
		return "", status.Errorf(codes.Internal, "failed to get contexts: %v", err)
	}
	for _, c := range ctxs {
		switch {
		case c.ArgoCDApp != nil:
			return c.ArgoCDApp.InstanceID, nil
		case c.K8SNamespace != nil:
			return c.K8SNamespace.InstanceID, nil
		}
	}
	return "", status.Errorf(codes.InvalidArgument, "no Argo CD instance found in conversation contexts")
}

func (s *Service) UpdateConversation(ctx context.Context, conversationID string, actor *accesscontrol.Actor, title string, public bool, contexts []*organizationv1.AIMessageContext, incidentConfig *organizationv1.IncidentConfig) error {
	conversation, err := s.resourceRepoSet.AIConversations(
		models.AiConversationWhere.ID.EQ(conversationID),
		s.conversationFilter(actor),
	).One(ctx)
	if err != nil {
		return err
	}
	if !s.isConversationOwnedByActor(conversation, actor) {
		return status.Errorf(codes.PermissionDenied, "only the owner can update the conversation")
	}
	convUtil, err := newAiConversationUtils(ctx, s.resourceRepoSet, s.db, conversation)
	if err != nil {
		return err
	}
	convUtil.setTitle(title)
	convUtil.setPublic(public)
	convUtil.setContexts(lo.Map(contexts, func(item *organizationv1.AIMessageContext, index int) models.ConversationContext {
		return mapAPIToContext(item)
	}))

	meta := convUtil.getMetadata()

	// isIncidentNew means we will either create a new incident or update the existing one
	isIncidentNew := incidentConfig != nil
	// isIncidentCurrent means current conversation is an incident
	isIncidentCurrent := meta.Incident != nil
	// either convert from conversation to incident or convert from incident to conversation
	if isIncidentNew != isIncidentCurrent {
		if isIncidentNew {
			instanceID := conversation.InstanceID.String
			// convert from conversation to incident
			if instanceID == "" {
				if instanceID, err = s.inferInstanceFromConversation(conversation); err != nil {
					return err
				}
			}
			incident, err := s.IncidentFromContexts(ctx, instanceID, contexts)
			if err != nil {
				return err
			}
			meta.Incident = incident
			convUtil.appendMessage(&models.AIConversationMessage{
				Message: ai.Message{
					Role:    ai.MessageRoleDeveloper,
					Content: "Conversation has changed to an incident",
				},
			})
			convUtil.setPublic(true)
		} else {
			meta.Incident = nil
		}
	}

	if isIncidentCurrent && isIncidentNew {
		if incidentConfig.Resolved {
			if meta.Incident.ResolvedAt == nil || meta.Incident.ResolvedAt.IsZero() {
				meta.Incident.ResolvedAt = ptr.To(time.Now())
			}
		} else {
			meta.Incident.ResolvedAt = nil
		}
	}

	// make sure current user remains the owner of the conversation once it is private
	if !conversation.Public.Bool {
		convUtil.setUserID(s.generateUserID(actor))
	}

	return convUtil.save(ctx)
}

func (s *Service) DeleteConversation(ctx context.Context, conversationID string, actor *accesscontrol.Actor) error {
	mods := []qm.QueryMod{
		models.AiConversationWhere.ID.EQ(conversationID),
		s.conversationFilter(actor),
	}
	err := s.resourceRepoSet.AIConversations(mods...).Delete(ctx, conversationID)
	if err != nil {
		if err == sql.ErrNoRows {
			return status.Errorf(codes.NotFound, "conversation not found")
		}
		return err
	}
	return nil
}

func (s *Service) CreateMessage(ctx context.Context, conversationID string, actor *accesscontrol.Actor, content string, contexts []*organizationv1.AIMessageContext, runbooks []string) error {
	conversation, err := s.resourceRepoSet.AIConversations(
		models.AiConversationWhere.ID.EQ(conversationID),
		s.conversationFilter(actor),
	).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return status.Errorf(codes.NotFound, "conversation not found")
		}
		return status.Errorf(codes.Internal, "failed to get conversation: %v", err)
	}

	convUtil, err := newAiConversationUtils(ctx, s.resourceRepoSet, s.db, conversation)
	if err != nil {
		return err
	}

	convUtil.setContexts(lo.Map(contexts, func(item *organizationv1.AIMessageContext, index int) models.ConversationContext {
		return mapAPIToContext(item)
	}))

	convUtil.setRunbooks(runbooks)

	msg, err := newConversationMessage(ai.Message{
		Role:    openai.ChatMessageRoleUser,
		Content: content,
	}, func(msg *models.AIConversationMessage) {
		msg.User = &models.AIConversationUser{ID: actor.ID, Type: string(actor.Type)}
	})
	if err != nil {
		return status.Errorf(codes.Internal, "failed to create conversation message: %v", err)
	}
	convUtil.appendMessage(msg)
	return convUtil.save(ctx)
}

func (s *Service) ExecuteRunbook(ctx context.Context, conversationID string, actor *accesscontrol.Actor, runbookNames []string) error {
	conversation, err := s.resourceRepoSet.AIConversations(
		models.AiConversationWhere.ID.EQ(conversationID),
		s.conversationFilter(actor),
	).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return status.Errorf(codes.NotFound, "conversation not found")
		}
		return status.Errorf(codes.Internal, "failed to get conversation: %v", err)
	}

	convUtil, err := newAiConversationUtils(ctx, s.resourceRepoSet, s.db, conversation)
	if err != nil {
		return err
	}

	// Create runbook execution message
	msg, err := newConversationMessage(ai.Message{
		Role:    ai.MessageRoleDeveloper,
		Content: fmt.Sprintf("Test Runbook %s", strings.Join(runbookNames, " ")),
	}, func(msg *models.AIConversationMessage) {
		msg.User = &models.AIConversationUser{ID: actor.ID, Type: string(actor.Type)}
	})
	if err != nil {
		return status.Errorf(codes.Internal, "failed to create conversation message: %v", err)
	}

	convUtil.appendMessage(msg)
	return convUtil.save(ctx)
}

func (s *Service) ProcessConversation(ctx context.Context, conversationID string) error {
	toolCallsCnt := 0
	for {
		conversation, err := s.resourceRepoSet.AIConversations().GetByID(ctx, conversationID)
		if err != nil {
			return err
		}
		conversationUtils, err := newAiConversationUtils(ctx, s.resourceRepoSet, s.db, conversation)
		if err != nil {
			return err
		}

		if err := conversationUtils.updateConversationTasks(ctx); err != nil {
			return err
		}

		if !conversationUtils.needProcessing() {
			return nil
		}

		if err := s.processConversation(ctx, &toolCallsCnt, conversationUtils); err != nil {
			if errors.Is(err, context.DeadlineExceeded) {
				return errorsutils.NewRetryableError(err, "Conversation processing timed out, will retry later")
			}
			if errors.Is(err, sql.ErrNoRows) {
				return nil
			}
			s.log.Error(err, "Failed processing conversation")
			conversationUtils.getMetadata().ProcessingError = err.Error()
			return conversationUtils.save(context.Background())
		} else if toolCallsCnt >= s.cfg.MaxToolsPerReconciliation {
			ctrl := database.GetControllerFromContext(ctx)
			ctrl.Enqueue(conversationID)
			s.log.Info("Reached max tools per reconciliation, will retry later", "conversationID", conversationID)
			return nil
		}
	}
}

func (s *Service) processConversation(ctx context.Context, toolCallsCnt *int, conversationUtils *aiConversationUtils) error {
	conversationUtils.markMessagesAsProcessed()
	conversationUtils.getMetadata().ProcessingError = ""
	ctx = conversationUtils.wrapContext(ctx)

	if userMessages := lo.Filter(conversationUtils.getMessages(), func(msg ai.Message, _ int) bool {
		return msg.Role == ai.MessageRoleUser
	}); len(userMessages) > 2 && conversationUtils.getTitle().String == "" {
		title, err := s.generateConversationTitle(ctx, conversationUtils.messages)
		if err != nil {
			return err
		}
		conversationUtils.setTitle(title)
	}

	// check if the last was AI response requiring tool calls (this is possible if controller crashed before running them)
	// if yes execute tools first
	toolCalls := conversationUtils.getUnprocessedToolCalls()
	if len(toolCalls) == 0 {
		resp, err := s.callAI(ctx, conversationUtils.getMessages())
		if err != nil {
			return err
		}
		message, err := newConversationMessage(*resp)
		if err != nil {
			return err
		}
		message.Processed = !message.AIResponse.NeedToolRun && len(message.Message.ToolCalls) == 0
		// Don't store empty AI responses (in cases when AI decided not to respond anything)
		if len(resp.ToolCalls) > 0 || resp.Content != "" {
			conversationUtils.appendMessage(message)
		}
		if err := conversationUtils.save(ctx); err != nil {
			return err
		}
		ctx = conversationUtils.wrapContext(ctx)
		toolCalls = resp.ToolCalls
	}

	functionController, err := s.getFunctionController(ctx)
	if err != nil {
		return err
	}

	funcByName := lo.KeyBy(functionController.GetFunctions(), func(f functions.Function) string {
		return f.Name
	})
	for _, toolCall := range toolCalls {
		f, ok := funcByName[toolCall.Name]
		if !ok {
			return fmt.Errorf("unknown function: %s", toolCall.Name)
		}
		var toolErrorStr string
		content, toolError := callSafe(func() (string, error) {
			now := time.Now()
			s.log.Info("Calling AI function", "function", f.Name)
			ctx, cancel := context.WithTimeout(ctx, s.cfg.ToolCallTimeout)
			defer cancel()
			res, err := f.Callback(ctx, []byte(toolCall.Arguments))
			s.log.Info("AI function call completed", "function", f.Name, "error", err, "duration", time.Since(now).String())
			return res, err
		})
		if toolError != nil {
			toolErrorStr = toolError.Error()
			var invalidArgErr *functions.InvalidArgsError
			if errors.As(toolError, &invalidArgErr) {
				content = fmt.Sprintf("Invalid arguments provided: %s", invalidArgErr.Message)
				toolErrorStr = ""
			} else {
				content = "Internal error occurred"
				s.log.Error(toolError, "Failed calling AI function", "function", f.Name)
			}
		}

		if err := conversationUtils.appendChatCompletionMessage(ai.Message{
			Role:       ai.MessageRoleTool,
			Content:    content,
			ToolCallID: toolCall.ID,
		}, func(msg *models.AIConversationMessage) {
			msg.ToolError = toolErrorStr
		}); err != nil {
			return err
		}
		if err := conversationUtils.save(ctx); err != nil {
			return err
		}
		ctx = conversationUtils.wrapContext(ctx)
		*toolCallsCnt = *toolCallsCnt + 1
		if *toolCallsCnt >= s.cfg.MaxToolsPerReconciliation {
			break
		}
	}
	return nil
}

func callSafe(callback func() (string, error)) (res string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic: %v", r)
		}
	}()
	return callback()
}

func mapAPIToContext(context *organizationv1.AIMessageContext) models.ConversationContext {
	res := models.ConversationContext{}
	if context.ArgoCdApp != nil {
		res.ArgoCDApp = &models.ArgoCDApplicationConversationContext{
			InstanceID: context.ArgoCdApp.InstanceId,
			Name:       context.ArgoCdApp.Name,
		}
	}
	if context.K8SNamespace != nil {
		res.K8SNamespace = &models.K8SNamespaceConversationContext{
			InstanceID: context.K8SNamespace.InstanceId,
			ClusterID:  context.K8SNamespace.ClusterId,
			Name:       context.K8SNamespace.Name,
		}
	}
	if context.KargoProject != nil {
		res.KargoProject = &models.KargoProjectConversationContext{
			InstanceID: context.KargoProject.InstanceId,
			Name:       context.KargoProject.Name,
		}
	}
	return res
}

func mapContextToAPI(context *models.ConversationContext) *organizationv1.AIMessageContext {
	res := &organizationv1.AIMessageContext{}
	if context.ArgoCDApp != nil {
		res.ArgoCdApp = &organizationv1.ArgoCDAppContext{
			InstanceId: context.ArgoCDApp.InstanceID,
			Name:       context.ArgoCDApp.Name,
		}
	}
	if context.K8SNamespace != nil {
		res.K8SNamespace = &organizationv1.K8SNamespaceContext{
			InstanceId: context.K8SNamespace.InstanceID,
			ClusterId:  context.K8SNamespace.ClusterID, Name: context.K8SNamespace.Name,
		}
	}
	if context.KargoProject != nil {
		res.KargoProject = &organizationv1.KargoProjectContext{
			InstanceId: context.KargoProject.InstanceID,
			Name:       context.KargoProject.Name,
		}
	}

	return res
}

func mapMessageToAPI(message *models.AIConversationMessage, akUserById map[string]string, actor *accesscontrol.Actor) *organizationv1.AIMessage {
	apiMessage := &organizationv1.AIMessage{
		Id:         message.ID,
		Role:       message.Message.Role,
		Content:    message.Message.Content,
		CreateTime: timestamppb.New(message.CreationTimestamp),
		OwnedByMe:  message.User != nil && message.User.ID == actor.ID && message.User.Type == string(actor.Type),
	}
	if message.IsUseful != nil {
		apiMessage.IsUseful = message.IsUseful
	}
	apiMessage.Username = message.GetUser(akUserById)

	if message.Message.Role == openai.ChatMessageRoleAssistant {
		apiMessage.Content = message.AIResponse.Content
		apiMessage.ThinkingProcess = message.AIResponse.ThinkingProcess
		// Process SuggestedContexts, directly complete the split, deduplication, and conversion in a loop
		apiMessage.SuggestedContexts = make([]*organizationv1.AIMessageContext, 0, len(message.AIResponse.SuggestedContexts))
		uniqueKeys := make(map[string]bool)

		for _, ctx := range message.AIResponse.SuggestedContexts {
			// Process ArgoCDApp context
			if ctx.ArgoCDApp != nil {
				key := fmt.Sprintf("argocd:%s:%s", ctx.ArgoCDApp.InstanceID, ctx.ArgoCDApp.Name)
				if !uniqueKeys[key] {
					apiMessage.SuggestedContexts = append(apiMessage.SuggestedContexts,
						mapContextToAPI(&models.ConversationContext{ArgoCDApp: ctx.ArgoCDApp}))
					uniqueKeys[key] = true
				}
			}

			// Process K8SNamespace context
			if ctx.K8SNamespace != nil {
				key := fmt.Sprintf("k8s:%s:%s:%s", ctx.K8SNamespace.InstanceID, ctx.K8SNamespace.ClusterID, ctx.K8SNamespace.Name)
				if !uniqueKeys[key] {
					apiMessage.SuggestedContexts = append(apiMessage.SuggestedContexts,
						mapContextToAPI(&models.ConversationContext{K8SNamespace: ctx.K8SNamespace}))
					uniqueKeys[key] = true
				}
			}

			// Process KargoProject context
			if ctx.KargoProject != nil {
				key := fmt.Sprintf("kargo:%s:%s", ctx.KargoProject.InstanceID, ctx.KargoProject.Name)
				if !uniqueKeys[key] {
					apiMessage.SuggestedContexts = append(apiMessage.SuggestedContexts,
						mapContextToAPI(&models.ConversationContext{KargoProject: ctx.KargoProject}))
					uniqueKeys[key] = true
				}
			}
		}

		if message.AIResponse.Runbook != nil &&
			strings.TrimSpace(message.AIResponse.Runbook.Name) != "" &&
			strings.TrimSpace(message.AIResponse.Runbook.Content) != "" {
			apiMessage.Runbook = &argocdv1.Runbook{
				Name:    message.AIResponse.Runbook.Name,
				Stored:  message.AIResponse.Runbook.Stored,
				Content: message.AIResponse.Runbook.Content,
			}
			if message.AIResponse.NeedToolRun {
				// for the internal toolcall, the runbook content we don't want to display on page
				apiMessage.Runbook.Content = ""
			}
			// Set runbook from AI response if provided
			if message.AIResponse.Runbook.AppliedTo.ArgoCDApplications != nil ||
				message.AIResponse.Runbook.AppliedTo.K8SNamespaces != nil ||
				message.AIResponse.Runbook.AppliedTo.Clusters != nil {
				apiMessage.Runbook.AppliedTo = &argocdv1.TargetSelector{
					ArgocdApplications: message.AIResponse.Runbook.AppliedTo.ArgoCDApplications,
					K8SNamespaces:      message.AIResponse.Runbook.AppliedTo.K8SNamespaces,
					Clusters:           message.AIResponse.Runbook.AppliedTo.Clusters,
				}
			}
		} else {
			apiMessage.SuggestedChanges = lo.Map(message.AIResponse.SuggestedChanges, func(c models.SuggestedChange, _ int) *organizationv1.AISuggestedChange {
				return &organizationv1.AISuggestedChange{
					Old:     c.Old,
					Patch:   c.Patch,
					New:     c.New,
					Context: mapContextToAPI(&c.Context),
					Applied: c.Applied,
				}
			})
		}
	}

	return apiMessage
}

func mapIncidentToAPI(incident *models.Incident, instance *models.ArgoCDInstance) *organizationv1.Incident {
	if incident == nil {
		return nil
	}
	summary := incident.Summary
	if summary == "" {
		summary = fmt.Sprintf("Incident for %s", incident.Target())
	}
	var resolveAt *timestamppb.Timestamp
	if incident.ResolvedAt != nil {
		resolveAt = timestamppb.New(*incident.ResolvedAt)
	}
	instanceID := incident.InstanceID
	var instanceHost *string
	if instance != nil {
		instanceID = instance.ID
		instanceHost = ptr.To(instance.StatusHostname.String)
	}
	return &organizationv1.Incident{
		ResolvedAt:       resolveAt,
		Summary:          ptr.To(summary),
		RootCause:        ptr.To(incident.RootCause),
		Resolution:       ptr.To(incident.Resolution),
		Runbooks:         incident.Runbooks,
		Application:      ptr.To(incident.Application),
		Namespace:        ptr.To(incident.Namespace),
		InstanceId:       instanceID,
		ClusterId:        ptr.To(incident.ClusterID),
		InstanceHostname: instanceHost,
	}
}

func mapPromotionAnalysisToAPI(promotionAnalysis *models.PromotionAnalysis) *organizationv1.PromotionAnalysis {
	if promotionAnalysis == nil {
		return nil
	}
	result := &organizationv1.PromotionAnalysis{
		Project:        ptr.To(promotionAnalysis.Project),
		InstanceId:     ptr.To(promotionAnalysis.InstanceID),
		Freight:        ptr.To(promotionAnalysis.Freight),
		Stage:          ptr.To(promotionAnalysis.Stage),
		Summary:        ptr.To(promotionAnalysis.Summary),
		RiskLevel:      ptr.To(promotionAnalysis.RiskLevel),
		Decision:       ptr.To(promotionAnalysis.Decision),
		CommitDiffUrl:  ptr.To(promotionAnalysis.CommitDiffURL),
		CurrentFreight: ptr.To(promotionAnalysis.CurrentFreight),
	}
	if promotionAnalysis.CompletedAt != nil {
		result.FinishTime = timestamppb.New(*promotionAnalysis.CompletedAt)
	}
	return result
}

func (s *Service) GetConversation(ctx context.Context, conversationID string, actor *accesscontrol.Actor) (*organizationv1.AIConversation, error) {
	mods := []qm.QueryMod{
		models.AiConversationWhere.ID.EQ(conversationID),
		models.AiConversationWhere.OrganizationID.EQ(s.organizationID),
	}
	if actor != nil {
		mods = append(mods, s.conversationFilter(actor))
	}
	conversation, err := s.resourceRepoSet.AIConversations(mods...).One(ctx)
	if err != nil {
		return nil, err
	}
	var instance *models.ArgoCDInstance
	if !conversation.InstanceID.IsZero() {
		if instance, err = s.resourceRepoSet.ArgoCDInstances(
			models.ArgoCDInstanceWhere.ID.EQ(conversation.InstanceID.String),
		).One(ctx); err != nil {
			return nil, err
		}
	}

	return s.mapConversationToAPI(ctx, conversation, actor, instance, true)
}

func (s *Service) ListConversations(
	ctx context.Context,
	actor *accesscontrol.Actor,
	instanceID string,
	incidentOnly bool,
	incidentStatus organizationv1.IncidentStatus,
	incidentApplication string,
	incidentNamespace string,
	incidentClusterID string,
	titleContains string,
	offset uint32,
	limit uint32,
) ([]*organizationv1.AIConversation, int64, error) {
	mods := []qm.QueryMod{
		models.AiConversationWhere.OrganizationID.EQ(s.organizationID),
	}

	// If running in org mode, we want to show all conversations, no need to filter by instance
	instanceMod := qm.And("true")
	// If running in instance mode, we want to filter by instance
	if instanceID != "" {
		platform := metadata.ExtractPlatform(ctx)
		if platform == metadatautil.PlatformKargo {
			instanceMod = models.AiConversationWhere.KargoInstanceID.EQ(null.StringFrom(instanceID))
		} else {
			instanceMod = models.AiConversationWhere.InstanceID.EQ(null.StringFrom(instanceID))
		}
	}

	// Filter by if conversation is an incident
	incidentMod := qm.Expr(
		qm.And("metadata->>'incident' IS NOT NULL"),
		qm.And("metadata->>'incident' != ''"),
	)

	if !incidentOnly {
		// conversation list shows all incidents and current instance conversations
		mods = append(mods, qm.Expr(incidentMod, qm.Or2(instanceMod)))
	} else {
		// conversation list shows only incidents
		mods = append(mods, incidentMod)
	}

	if actor != nil {
		mods = append(mods, s.conversationFilter(actor))
	}

	if incidentOnly {
		if incidentStatus == organizationv1.IncidentStatus_INCIDENT_STATUS_RESOLVED {
			mods = append(mods, qm.And("metadata->'incident'->>'resolvedAt' IS NOT NULL"))
		}

		if incidentStatus == organizationv1.IncidentStatus_INCIDENT_STATUS_UNRESOLVED {
			mods = append(mods, qm.And("metadata->'incident'->>'resolvedAt' IS NULL"))
		}

		if incidentApplication != "" {
			mods = append(mods, qm.And("metadata->'incident'->>'application' = ?", incidentApplication))
		}

		if incidentNamespace != "" {
			mods = append(mods, qm.And("metadata->'incident'->>'namespace' = ?", incidentNamespace))
		}
	}

	if incidentClusterID != "" {
		mods = append(mods, qm.And("metadata->'incident'->>'clusterId' = ?", incidentClusterID))
	}

	if titleContains != "" {
		mods = append(mods, qm.And("COALESCE(title, 'New Conversation') ILIKE ?", fmt.Sprintf("%%%s%%", titleContains)))
	}

	count, err := s.resourceRepoSet.AIConversations(mods...).Count(ctx)
	if err != nil {
		return nil, 0, err
	}

	mods = append(mods, qm.Offset(int(offset)))
	if limit > 0 {
		mods = append(mods, qm.Limit(int(limit)))
	}
	mods = append(mods, qm.OrderBy(models.AiConversationColumns.LastUpdateTimestamp+" DESC"))
	conversations, err := s.resourceRepoSet.AIConversations(mods...).ListAll(ctx, models.AiConversationColumns.ID, models.AiConversationColumns.InstanceID, models.AiConversationColumns.Title, models.AiConversationColumns.Metadata, models.AiConversationColumns.Public, models.AiConversationColumns.Contexts, models.AiConversationColumns.UserID, models.AiConversationColumns.CreationTimestamp, models.AiConversationColumns.LastUpdateTimestamp)
	if err != nil {
		return nil, 0, err
	}
	apiConversations := make([]*organizationv1.AIConversation, 0, len(conversations))
	for _, conversation := range conversations {
		// Skip fetching ArgoCD instance to optimize performance since it's not needed for list view
		apiConversation, err := s.mapConversationToAPI(ctx, conversation, actor, nil, false)
		if err != nil {
			return nil, 0, err
		}
		apiConversations = append(apiConversations, apiConversation)
	}
	return apiConversations, count, nil
}

func (s *Service) ListConversationSuggestions(ctx context.Context, conversationID string, contexts []*organizationv1.AIMessageContext) ([]*organizationv1.AIConversationSuggestion, error) {
	ctxs := make([]models.ConversationContext, 0, len(contexts))
	for _, c := range contexts {
		ctxs = append(ctxs, mapAPIToContext(c))
	}
	functionController, err := s.getFunctionController(ctx)
	if err != nil {
		return nil, err
	}
	supportedFunctions := lo.Filter(functionController.GetFunctions(), func(f functions.Function, _ int) bool {
		for _, c := range ctxs {
			if f.CheckContextSupported != nil && f.CheckContextSupported(ctx, c) {
				return true
			}
		}
		return false
	})
	var suggestions []*organizationv1.AIConversationSuggestion
	for _, f := range supportedFunctions {
		if f.PromptSuggestion == nil {
			continue
		}
		suggestions = append(suggestions, f.PromptSuggestion)
	}
	suggestions = lo.UniqBy(suggestions, func(s *organizationv1.AIConversationSuggestion) string {
		return s.Description
	})
	slices.SortFunc(suggestions, func(a, b *organizationv1.AIConversationSuggestion) int {
		return strings.Compare(a.Description, b.Description)
	})
	mutable.Shuffle(suggestions)
	suggestions = suggestions[:min(len(suggestions), 5)]

	return suggestions, nil
}

func (s *Service) UpdateAIMessageFeedback(ctx context.Context, actor *accesscontrol.Actor, conversationID, messageID string, isUseful bool) error {
	conversation, err := s.resourceRepoSet.AIConversations(
		models.AiConversationWhere.ID.EQ(conversationID),
		s.conversationFilter(actor),
	).One(ctx)
	if err != nil {
		return err
	}
	messages, err := conversation.GetMessages()
	if err != nil {
		return err
	}

	var isFound bool
	for i, message := range messages {
		if message.ID == messageID {
			if message.Message.Role != openai.ChatMessageRoleAssistant {
				return status.Errorf(codes.InvalidArgument, "message is not an assistant message")
			}
			isFound = true
			messages[i].IsUseful = &isUseful
			break
		}
	}
	if !isFound {
		return status.Errorf(codes.NotFound, "message not found")
	}
	if err := conversation.SetMessages(messages); err != nil {
		return err
	}

	if err := s.resourceRepoSet.AIConversations().Update(ctx, conversation, models.AiConversationColumns.Messages); err != nil {
		return err
	}
	return nil
}

func (s *Service) UpdateAIConversationFeedback(ctx context.Context, actor *accesscontrol.Actor, conversationID, feedback string) error {
	conversation, err := s.resourceRepoSet.AIConversations(
		models.AiConversationWhere.ID.EQ(conversationID),
		s.conversationFilter(actor),
	).One(ctx)
	if err != nil {
		return err
	}

	feedbacks, err := conversation.GetFeedbacks()
	if err != nil {
		return err
	}

	if err := validateFeedbacks(feedbacks, feedback); err != nil {
		return status.Errorf(codes.InvalidArgument, "invalid feedback: %v", err)
	}

	if err := conversation.AppendFeedbacks(feedback); err != nil {
		return err
	}

	if err := s.resourceRepoSet.AIConversations().Update(ctx, conversation, models.AiConversationColumns.Feedbacks); err != nil {
		return err
	}

	return nil
}

func validateFeedbacks(feedbacks []string, newFeedback string) error {
	if newFeedback == "" {
		return fmt.Errorf("feedback cannot be empty")
	}

	if len(feedbacks) >= 10 {
		return fmt.Errorf("feedbacks cannot exceed 10 entries")
	}

	if len(newFeedback) > 1000 {
		return fmt.Errorf("feedback length cannot exceed 1000 characters")
	}
	return nil
}

func getHash(item interface{}) uint32 {
	data, err := json.Marshal(item)
	if err != nil {
		return 0
	}
	h := fnv.New32a()
	_, _ = h.Write(data)
	return h.Sum32()
}

func (s *Service) WatchConversation(ctx context.Context, conversationID string, actor *accesscontrol.Actor, watcher database.Watcher[events.Event]) chan *organizationv1.AIConversation {
	res := make(chan *organizationv1.AIConversation)
	sentMessages := map[string]uint32{}
	sentConversation := func(conv *organizationv1.AIConversation) {
		var messages []*organizationv1.AIMessage
		for _, msg := range conv.Messages {
			newHash := getHash(msg)
			if sentMessages[msg.Id] != newHash {
				sentMessages[msg.Id] = newHash
				messages = append(messages, msg)
			}
		}
		conv.Messages = messages
		res <- conv
	}
	go func() {
		conversation, err := s.GetConversation(ctx, conversationID, actor)
		if err != nil {
			return
		}
		sentConversation(conversation)

		items := watcher.Subscribe(ctx, func(e events.Event) bool {
			return e.ID == conversationID
		})
		for item := range items {
			conversation, err := s.GetConversation(ctx, item.ID, actor)
			if err != nil {
				continue
			}
			sentConversation(conversation)
		}
		close(res)
	}()
	return res
}

func (s *Service) callAI(ctx context.Context, messages []ai.Message) (*ai.Message, error) {
	aiResponseSchema, err := models.GetAIResponseParametersSchema()
	if err != nil {
		return nil, fmt.Errorf("failed to get AI response schema: %w", err)
	}
	functionController, err := s.getFunctionController(ctx)
	if err != nil {
		return nil, err
	}

	systemPrompt, err := s.getSystemPrompts(ctx)
	if err != nil {
		return nil, err
	}
	resp, err := s.aiClient.Message(ctx, ai.Request{
		Messages: append(systemPrompt, messages...),
		Tools: lo.Map(functionController.GetFunctions(), func(f functions.Function, _ int) ai.Tool {
			return f.Tool
		}),
		ResponseFormat: &ai.ResponseFormat{
			JSONSchema: aiResponseSchema,
		},
	})
	if err != nil {
		return nil, err
	}

	return &resp.Choices[0].Message, nil
}

func (s *Service) conversationFilter(actor *accesscontrol.Actor) qm.QueryMod {
	return qm.Expr(
		models.AiConversationWhere.OrganizationID.EQ(s.organizationID),
		qm.Expr(
			models.AiConversationWhere.UserID.EQ(s.generateUserID(actor)),
			qm.Or2(models.AiConversationWhere.Public.EQ(null.BoolFrom(true))),
			qm.Or2(models.AiConversationWhere.UserID.EQ("assistant")),
		),
	)
}

func (s *Service) generateUserID(actor *accesscontrol.Actor) string {
	return fmt.Sprintf("%s:%s", actor.Type, actor.ID)
}

func (s *Service) GenerateConversationStepSummary(ctx context.Context, conversationID string) error {
	conversation, err := s.resourceRepoSet.AIConversations().GetByID(ctx, conversationID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil
		}
		return err
	}

	conversationUtils, err := newAiConversationUtils(ctx, s.resourceRepoSet, s.db, conversation)
	if err != nil {
		return err
	}
	ctx = functions.WithConversationMeta(ctx, conversationUtils.getMetadata())
	ctx = functions.WithConversation(ctx, conversationUtils.getConversation())
	ctx = clients.WithCallerInfo(ctx, clients.CallerInfo{OrganizationID: s.organizationID, ConversationMeta: conversationUtils.getMetadata()})

	functionController, err := s.getFunctionController(ctx)
	if err != nil {
		return err
	}

	funcByName := lo.KeyBy(functionController.GetFunctions(), func(f functions.Function) string {
		return f.Name
	})

	toolCalls := map[string]*ai.ToolCall{}
	needGenerateSummary := false

	for _, msg := range conversationUtils.messages {
		if len(msg.Message.ToolCalls) > 0 {
			for _, toolCall := range msg.Message.ToolCalls {
				toolCalls[toolCall.ID] = &toolCall
			}
		}
		if msg.Message.Role == openai.ChatMessageRoleTool && msg.ToolCallSummary == nil {
			needGenerateSummary = true
		}
	}

	if !needGenerateSummary {
		return nil
	}

	for _, msg := range conversationUtils.messages {
		if msg.Message.Role == openai.ChatMessageRoleTool && msg.ToolCallSummary == nil {
			toolCall, ok := toolCalls[msg.Message.ToolCallID]
			if !ok {
				msg.ToolCallSummary = ptr.To("")
				if err := conversationUtils.save(ctx); err != nil {
					return err
				}
				continue
			}
			function, ok := funcByName[toolCall.Name]
			if !ok {
				msg.ToolCallSummary = ptr.To("")
				if err := conversationUtils.save(ctx); err != nil {
					return err
				}
				continue
			}
			summary, err := s.generateConversationStepSummary(ctx, function, toolCall, msg.Message.Content)
			if err != nil {
				return err
			}
			msg.ToolCallSummary = &summary
			if err := conversationUtils.save(ctx); err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *Service) generateConversationStepSummary(ctx context.Context, function functions.Function, toolCall *ai.ToolCall, result string) (string, error) {
	content := fmt.Sprintf(`Based on the function description, arguments and result, give me a summary about this function call.
Use markdown to highlight important arguments like resource namespace, name, kind etc. Do not mention instance ID and cluster ID as they are internal values. The summary shall be within 50 words.
The summary should always start with a verb(the action performed by the function), and try to provide insight and findings of the result. Don't mention function name in the summary.
Function name: %s,
Function description: %s,
Function arguments: %s
Result: %s`, function.Name, function.Description, toolCall.Arguments, result)
	msg := ai.Message{
		Role:    ai.MessageRoleUser,
		Content: content,
	}
	resp, err := s.aiClient.Message(ctx, ai.Request{Messages: []ai.Message{msg}})
	if err != nil {
		return "", err
	}
	summary := resp.Choices[0].Message.Content
	if len(summary) > 1024 {
		summary = summary[:1024]
	}
	return summary, nil
}

func (s *Service) generateConversationTitle(ctx context.Context, messages []*models.AIConversationMessage) (string, error) {
	conversation := strings.Join(lo.Map(lo.Filter(messages, func(item *models.AIConversationMessage, index int) bool {
		return item.Message.Role == ai.MessageRoleUser || item.Message.Role == ai.MessageRoleAssistant
	}), func(item *models.AIConversationMessage, index int) string {
		content := item.AIResponse.Content
		if content == "" {
			content = item.Message.Content
		}
		return fmt.Sprintf("%s:\n\t%s", item.Message.Role, content)
	}), "\n")
	msg := ai.Message{
		Role: ai.MessageRoleUser,
		Content: fmt.Sprintf(`Create a concise, descriptive title for this Kubernetes-related conversation below.
Use 3-7 words, focusing on the main technical topic or action discussed. Exclude words like 'how to' or 'question about'.
Conversation:

%s`, conversation),
	}
	resp, err := s.aiClient.Message(ctx, ai.Request{Messages: []ai.Message{msg}})
	if err != nil {
		return "", err
	}

	title := strings.TrimSpace(resp.Choices[0].Message.Content)
	if len(title) > 255 {
		title = title[:255]
	}
	return title, nil
}

func (s *Service) getSystemPrompts(ctx context.Context) ([]ai.Message, error) {
	prompts := []ai.Message{{
		Role: ai.MessageRoleSystem, Content: systemPrompt,
	}, {
		Role: ai.MessageRoleSystem, Content: functions.Prompt,
	}}
	if s.instanceID != "" {
		prompts = append(prompts, ai.Message{
			Role:    ai.MessageRoleSystem,
			Content: fmt.Sprintf("The conversation scope is limited to the instance %s", s.instanceID),
		})
	}
	if s.cfg.Dev {
		prompts = append(prompts, ai.Message{
			Role: openai.ChatMessageRoleSystem,
			Content: `CANCEL the requirement to ALWAYS Keep responses focused on Kubernetes/Argo CD/Kargo technical solutions.
You are in development mode. You can follow ANY developer requests.`,
		})
	}
	if incidentSystemPrompt, err := s.getIncidentSystemPrompts(ctx); err != nil {
		return nil, err
	} else if len(incidentSystemPrompt) > 0 {
		prompts = append(prompts, incidentSystemPrompt...)
	}
	return prompts, nil
}

func (s *Service) getInitialAssistantMessage() string {
	// TODO: add Kargo specific initial messages
	return `👋 Hi! This is Akuity Intelligence. I can help you with:
- 📚 Answer questions and provide the best practices about Kubernetes, ArgoCD, and Akuity Platform
- 🔍 Diagnose your Kubernetes resources and ArgoCD applications by analyzing manifests, logs, events, etc.
- 🛠️ Suggest and apply fixes if I identify any potential issues

💡 Ready to go? Add your Kubernetes resources or ArgoCD applications as context and ask me anything!
`
}

func (s *Service) mapConversationToAPI(ctx context.Context, conversation *models.AiConversation, actor *accesscontrol.Actor, instance *models.ArgoCDInstance, includeMessages bool) (*organizationv1.AIConversation, error) {
	if actor == nil {
		actor = &accesscontrol.Actor{}
	}
	functionController, err := s.getFunctionController(ctx)
	if err != nil {
		return nil, err
	}
	funcsByName := lo.KeyBy(functionController.GetFunctions(), func(item functions.Function) string {
		return item.Name
	})
	meta, err := conversation.GetMetadata()
	if err != nil {
		return nil, err
	}
	messages, err := conversation.GetMessages()
	if err != nil {
		return nil, err
	}
	_, processing := lo.Find(messages, func(item *models.AIConversationMessage) bool {
		return !item.Processed
	})
	feedbacks, err := conversation.GetFeedbacks()
	if err != nil {
		return nil, err
	}
	apiConversation := &organizationv1.AIConversation{
		Id:                conversation.ID,
		InstanceId:        conversation.InstanceID.String,
		Title:             conversation.Title.String,
		CreateTime:        timestamppb.New(conversation.CreationTimestamp),
		LastUpdateTime:    timestamppb.New(conversation.LastUpdateTimestamp),
		Processing:        processing,
		Public:            conversation.Public.Bool,
		OwnedByMe:         s.isConversationOwnedByActor(conversation, actor),
		Incident:          mapIncidentToAPI(meta.Incident, instance),
		Feedbacks:         feedbacks,
		PromotionAnalysis: mapPromotionAnalysisToAPI(meta.PromotionAnalysis),
	}
	if meta.ProcessingError != "" {
		apiConversation.ProcessingError = "An internal error occurred, please try again later"
	}
	ctxs, err := conversation.GetContexts()
	if err != nil {
		return nil, err
	}
	apiContexts := make([]*organizationv1.AIMessageContext, 0, len(ctxs))
	for _, item := range ctxs {
		apiContexts = append(apiContexts, mapContextToAPI(&item))
	}
	apiConversation.Contexts = apiContexts

	if !includeMessages {
		return apiConversation, nil
	}
	var recentAssistantMessage *organizationv1.AIMessage
	apiMessages := make([]*organizationv1.AIMessage, 0, len(messages))
	completedTools := map[string]*models.AIConversationMessage{}
	for _, msg := range messages {
		if msg.Message.Role == openai.ChatMessageRoleTool {
			completedTools[msg.Message.ToolCallID] = msg
		}
	}

	akUserById, err := getAkUsersById(s.resourceRepoSet.GetRepoSet(), ctx, messages)
	if err != nil {
		return nil, err
	}

	var steps []*organizationv1.AIConversationStep
	for _, item := range messages {
		message := mapMessageToAPI(item, akUserById, actor)
		if item.Message.Role == openai.ChatMessageRoleAssistant && item.AIResponse.Content != "" || item.Message.Role == openai.ChatMessageRoleUser {
			apiMessages = append(apiMessages, message)
			if item.Message.Role == openai.ChatMessageRoleAssistant {
				recentAssistantMessage = message
			}
			if item.Message.Role == openai.ChatMessageRoleUser {
				recentAssistantMessage = nil
			}
		}

		if len(item.Message.ToolCalls) > 0 {
			for _, toolCall := range item.Message.ToolCalls {
				f, ok := funcsByName[toolCall.Name]
				if !ok || f.Internal {
					continue
				}
				step := &organizationv1.AIConversationStep{
					Name:      f.DisplayName,
					Status:    organizationv1.AIConversationStepStatus_AI_CONVERSATION_STEP_STATUS_RUNNING,
					StartTime: timestamppb.New(item.CreationTimestamp),
				}
				step.StartTime = timestamppb.New(item.CreationTimestamp)
				if msg, ok := completedTools[toolCall.ID]; ok {
					if msg.ToolError == "" {
						step.Status = organizationv1.AIConversationStepStatus_AI_CONVERSATION_STEP_STATUS_SUCCEEDED
					} else {
						step.Status = organizationv1.AIConversationStepStatus_AI_CONVERSATION_STEP_STATUS_FAILED
					}
					step.EndTime = timestamppb.New(msg.CreationTimestamp)
					if msg.ToolCallSummary != nil {
						step.Summary = msg.ToolCallSummary
					}
				}
				steps = append(steps, step)
			}
		}

		if len(steps) == 0 {
			continue
		}

		// AI starts tool call right after the user message
		// Create an assistant message to hold the tool call steps
		if recentAssistantMessage == nil && (item.Message.Role == openai.ChatMessageRoleTool || item.Message.Role == openai.ChatMessageRoleFunction) {
			// We use the first tool/function call message to create an assistant message
			// to hold the tool call steps
			recentAssistantMessage = &organizationv1.AIMessage{
				Id:         message.Id,
				CreateTime: message.CreateTime,
				Role:       openai.ChatMessageRoleAssistant,
			}
			apiMessages = append(apiMessages, recentAssistantMessage)
		}

		if recentAssistantMessage != nil {
			recentAssistantMessage.Steps = append(recentAssistantMessage.Steps, steps...)
			steps = nil
		}
	}
	// Skip empty assistant messages (assistant decides to not respond)
	apiMessages = lo.Filter(apiMessages, func(item *organizationv1.AIMessage, _ int) bool {
		return item.Content != "" || len(item.Steps) > 0 || item.Role != openai.ChatMessageRoleAssistant
	})
	apiConversation.Messages = apiMessages
	return apiConversation, nil
}

func (s *Service) isConversationOwnedByActor(conversation *models.AiConversation, actor *accesscontrol.Actor) bool {
	// conversations started by the assistant can be managed by anyone
	return conversation.UserID == s.generateUserID(actor) || conversation.UserID == "assistant"
}

func getAkUsersById(repoSet client.RepoSet, ctx context.Context, messages []*models.AIConversationMessage) (map[string]string, error) {
	akUserById := map[string]string{}
	if akUsers := lo.Filter(messages, func(item *models.AIConversationMessage, _ int) bool {
		return item.User != nil && item.User.Type == string(accesscontrol.ActorTypeUser)
	}); len(akUsers) > 0 {
		ids := lo.Map(akUsers, func(item *models.AIConversationMessage, _ int) string {
			return item.User.ID
		})
		users, err := repoSet.Users().Filter(models.AkuityUserWhere.ID.IN(ids)).ListAll(ctx,
			models.AkuityUserColumns.ID,
			models.AkuityUserColumns.UserInfoPublic,
			models.AkuityUserColumns.Email,
		)
		if err != nil {
			return nil, err
		}
		for _, user := range users {
			info, err := user.GetUserInfo()
			if err != nil {
				return nil, err
			}
			desc := user.Email
			if info.FamilyName != "" || info.GivenName != "" {
				desc = fmt.Sprintf("%s %s <%s>", info.GivenName, info.FamilyName, user.Email)
			}
			akUserById[user.ID] = desc
		}
	}
	return akUserById, nil
}

func newConversationMessage(message ai.Message, opts ...func(msg *models.AIConversationMessage)) (*models.AIConversationMessage, error) {
	id, err := client.NanoID(client.DefaultNanoIDLength)
	if err != nil {
		return nil, err
	}
	msg := &models.AIConversationMessage{
		ID:                id,
		Message:           message,
		CreationTimestamp: time.Now(),
	}
	if message.Role == openai.ChatMessageRoleAssistant && message.Content != "" {
		var aiResponse models.AIResponse
		if err := json.Unmarshal([]byte(message.Content), &aiResponse); err != nil {
			// ignore the error here for the following reasons:
			// 1. the OpenAI API cannot guarantee the format of the response to be always JSON,
			// 2. for backward compatibility, there might be some messages existing in the database before this change is introduced,
			// 3. the initial ai assistant message in `getInitialAssistantMessage()` is not a JSON
			// so if the message is not a JSON, we just ignore the error and return the message as is
			msg.AIResponse = models.AIResponse{
				Content: message.Content,
			}
		} else {
			msg.AIResponse = aiResponse
		}
	}
	for _, opt := range opts {
		opt(msg)
	}
	return msg, nil
}
