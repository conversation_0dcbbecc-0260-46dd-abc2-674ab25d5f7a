package ai

import (
	"testing"

	"github.com/akuityio/akuity-platform/internal/utils/ai"
)

// TestRunbookExecution tests the runbook execution message creation
func TestRunbookExecution(t *testing.T) {
	// This is a basic test to verify the message creation logic
	// In a real test environment, you would need to set up the database and dependencies

	// Test message content generation
	expectedContent := "Test Runbook pod-oom-fix service-selector-mismatch"

	msg := ai.Message{
		Role:    ai.MessageRoleDeveloper,
		Content: expectedContent,
	}

	if msg.Role != ai.MessageRoleDeveloper {
		t.<PERSON><PERSON>("Expected role %s, got %s", ai.MessageRoleDeveloper, msg.Role)
	}

	if msg.Content != expectedContent {
		t.<PERSON>("Expected content %s, got %s", expectedContent, msg.Content)
	}

	t.Logf("Message created successfully: Role=%s, Content=%s", msg.Role, msg.Content)
}

// TestRunbookMessageFormat tests the message format matches the expected pattern
func TestRunbookMessageFormat(t *testing.T) {
	testCases := []struct {
		name         string
		runbookNames []string
		expected     string
	}{
		{
			name:         "single runbook",
			runbookNames: []string{"pod-oom-fix"},
			expected:     "Test Runbook pod-oom-fix",
		},
		{
			name:         "multiple runbooks",
			runbookNames: []string{"pod-oom-fix", "service-selector-mismatch"},
			expected:     "Test Runbook pod-oom-fix service-selector-mismatch",
		},
		{
			name:         "three runbooks",
			runbookNames: []string{"pod-oom-fix", "service-selector-mismatch", "deployment-rollback"},
			expected:     "Test Runbook pod-oom-fix service-selector-mismatch deployment-rollback",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			content := "Test Runbook " + joinRunbookNames(tc.runbookNames)
			if content != tc.expected {
				t.Errorf("Expected %s, got %s", tc.expected, content)
			}
		})
	}
}

// Helper function to simulate the string joining logic
func joinRunbookNames(names []string) string {
	result := ""
	for i, name := range names {
		if i > 0 {
			result += " "
		}
		result += name
	}
	return result
}

// TestRunbookPromptMatching tests if the message format matches the expected prompt pattern
func TestRunbookPromptMatching(t *testing.T) {
	// Test cases that should trigger the runbook workflow according to prompt.md
	testCases := []struct {
		name        string
		message     string
		shouldMatch bool
	}{
		{
			name:        "exact match - Test Runbook with names",
			message:     "Test Runbook pod-oom-fix service-selector-mismatch",
			shouldMatch: true,
		},
		{
			name:        "exact match - Test Runbook single name",
			message:     "Test Runbook pod-oom-fix",
			shouldMatch: true,
		},
		{
			name:        "case sensitive - test runbook lowercase",
			message:     "test runbook pod-oom-fix",
			shouldMatch: false, // According to prompt, it should be exact "Test Runbook"
		},
		{
			name:        "different phrase",
			message:     "Execute runbook pod-oom-fix",
			shouldMatch: false,
		},
		{
			name:        "Test Runbook without names",
			message:     "Test Runbook",
			shouldMatch: true, // Should ask for runbook names
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// According to prompt.md line 171: "Only trigger this workflow when user explicitly says "Test Runbook" followed by runbook names"
			hasTestRunbook := len(tc.message) >= 12 && tc.message[:12] == "Test Runbook"

			if hasTestRunbook != tc.shouldMatch {
				t.Errorf("Message '%s' match result %v, expected %v", tc.message, hasTestRunbook, tc.shouldMatch)
			}
		})
	}
}
